import { Router } from 'express';
import {  generateOrAdvanceRound, getPlayedMatchesWithWinners, getTournamentMatchesByRound, getWinnersOfRound,  updateMatchResult } from './Knockout.controller.js';
import { allowTo, protectedRouter } from '../auth/auth.controller.js';

const knockoutRouter = Router();
//! start the tournament and generate the round
knockoutRouter.post('/tournaments/:tournamentId/generate', protectedRouter, allowTo('owner'), generateOrAdvanceRound);
//! update the match result
knockoutRouter.patch('/match/:matchId/result',protectedRouter , allowTo('owner') ,updateMatchResult);

//! get all matches of tournament in each round
knockoutRouter.get('/tournaments/:tournamentId/matches', getTournamentMatchesByRound)
//! get the winner of each round
knockoutRouter.get('/tournament/:tournamentId/round-winners', getWinnersOfRound);
//! get the winner of each match
knockoutRouter.get('/tournaments/:tournamentId/matchresults', getPlayedMatchesWithWinners);


export default knockoutRouter;