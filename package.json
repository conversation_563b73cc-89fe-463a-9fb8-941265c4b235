{"name": "hagz", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "engines": {"node": "22.15.0"}, "license": "ISC", "dependencies": {"axios": "^1.9.0", "bcrypt": "^5.1.1", "cloudinary": "^1.30.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.2", "multer-storage-cloudinary": "^4.0.0", "uuid": "^11.1.0"}}